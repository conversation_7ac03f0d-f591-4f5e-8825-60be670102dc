/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: #333;
    overflow-x: hidden;
}

/* Image Optimization */
img {
    max-width: 100%;
    height: auto;
    display: block;
}

img[loading="lazy"] {
    opacity: 0;
    transition: opacity 0.3s ease;
}

img[loading="lazy"].loaded {
    opacity: 1;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    padding: 1rem 0;
    transition: all 0.3s ease;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.logo-img {
    height: 50px;
    width: auto;
    object-fit: contain;
}

.nav-logo h2 {
    color: #2d5a27;
    font-weight: 700;
    font-size: 1.8rem;
    margin: 0;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-link:hover {
    color: #2d5a27;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background: #333;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    color: white;
    padding: 120px 0 80px;
    text-align: center;
    position: relative;
    overflow: hidden;
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.hero-bg-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(45, 90, 39, 0.8) 0%, rgba(74, 124, 89, 0.8) 100%);
}

.hero-content {
    position: relative;
    z-index: 3;
    max-width: 800px;
    margin: 0 auto;
    padding: 0 20px;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.3rem;
    margin-bottom: 2.5rem;
    opacity: 0.9;
    font-weight: 300;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.btn {
    padding: 15px 30px;
    border: none;
    border-radius: 50px;
    font-size: 1.1rem;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    cursor: pointer;
    display: inline-block;
}

.btn-primary {
    background: white;
    color: #2d5a27;
}

.btn-primary:hover {
    background: #f0f0f0;
    transform: translateY(-2px);
}

.btn-secondary {
    background: transparent;
    color: white;
    border: 2px solid white;
}

.btn-secondary:hover {
    background: white;
    color: #2d5a27;
    transform: translateY(-2px);
}

.hero-stats {
    display: flex;
    justify-content: center;
    gap: 4rem;
    margin-top: 4rem;
    position: relative;
    z-index: 3;
}

.stat {
    text-align: center;
}

.stat h3 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stat p {
    font-size: 1rem;
    opacity: 0.8;
}

/* Section Styles */
section {
    padding: 80px 0;
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-header h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2d5a27;
    margin-bottom: 1rem;
}

.section-header p {
    font-size: 1.2rem;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
}

/* About Section */
.about {
    background: #f8f9fa;
}

.about-content {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 4rem;
    align-items: center;
}

.about-image {
    position: relative;
}

.about-img {
    width: 100%;
    height: 400px;
    object-fit: cover;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.about-text h3 {
    font-size: 1.8rem;
    color: #2d5a27;
    margin-bottom: 1rem;
    margin-top: 2rem;
}

.about-text h3:first-child {
    margin-top: 0;
}

.about-text p {
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 1.5rem;
    color: #555;
}

.values {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.value-item {
    text-align: center;
    padding: 2rem;
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.value-item:hover {
    transform: translateY(-5px);
}

.value-item i {
    font-size: 3rem;
    color: #2d5a27;
    margin-bottom: 1rem;
}

.value-item h4 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: #333;
}

.value-item p {
    color: #666;
    font-size: 1rem;
}

/* Programs Section */
.programs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.program-card {
    background: white;
    padding: 2.5rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border-top: 4px solid #2d5a27;
}

.program-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.program-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #2d5a27, #4a7c59);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
}

.program-icon i {
    font-size: 2rem;
    color: white;
}

.program-image {
    width: 100%;
    height: 200px;
    margin-bottom: 1.5rem;
    border-radius: 10px;
    overflow: hidden;
}

.program-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    transition: transform 0.3s ease;
}

.program-card:hover .program-img {
    transform: scale(1.05);
}

.program-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #333;
}

.program-card p {
    color: #666;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.program-card ul {
    list-style: none;
}

.program-card li {
    padding: 0.5rem 0;
    color: #555;
    position: relative;
    padding-left: 1.5rem;
}

.program-card li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #2d5a27;
    font-weight: bold;
}

/* Impact Section */
.impact {
    background: linear-gradient(135deg, #2d5a27 0%, #4a7c59 100%);
    color: white;
}

.impact .section-header h2,
.impact .section-header p {
    color: white;
}

.impact-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.impact-card {
    text-align: center;
    padding: 2rem;
    background: rgba(255,255,255,0.1);
    border-radius: 15px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
}

.impact-number {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.impact-label {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* Conservation Areas Section */
.conservation-areas {
    background: #f8f9fa;
}

.conservation-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.conservation-card {
    background: white;
    padding: 2.5rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border-left: 5px solid #2d5a27;
}

.conservation-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.conservation-image {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #2d5a27, #4a7c59);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
}

.conservation-image i {
    font-size: 2rem;
    color: white;
}

.conservation-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #333;
}

.conservation-card p {
    color: #666;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.conservation-stats {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.conservation-stats span {
    background: #f0f8f0;
    color: #2d5a27;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
}

.conservation-stats i {
    margin-right: 0.5rem;
}

/* Tree Campaign Section */
.tree-campaign {
    color: white;
    padding: 80px 0;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.campaign-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.campaign-bg-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
}

.campaign-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(45, 90, 39, 0.85) 0%, rgba(74, 124, 89, 0.85) 100%);
}

.tree-campaign .container {
    position: relative;
    z-index: 2;
}

.campaign-content h2 {
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
    font-weight: 700;
}

.campaign-content p {
    font-size: 1.2rem;
    margin-bottom: 3rem;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
    opacity: 0.9;
}

.campaign-stats {
    display: flex;
    justify-content: center;
    gap: 4rem;
    margin-bottom: 3rem;
}

.campaign-stat {
    text-align: center;
}

.campaign-stat h3 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.campaign-stat p {
    font-size: 1rem;
    opacity: 0.8;
    margin: 0;
}

.btn-large {
    padding: 18px 40px;
    font-size: 1.2rem;
}

/* Resources Section */
.resources {
    background: white;
}

.resources-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.resource-card {
    background: #f8f9fa;
    padding: 2.5rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    text-align: center;
    transition: transform 0.3s ease;
}

.resource-card:hover {
    transform: translateY(-5px);
}

.resource-card i {
    font-size: 3rem;
    color: #2d5a27;
    margin-bottom: 1.5rem;
}

.resource-image {
    width: 100%;
    height: 200px;
    margin-bottom: 1.5rem;
    border-radius: 10px;
    overflow: hidden;
}

.resource-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    transition: transform 0.3s ease;
}

.resource-card:hover .resource-img {
    transform: scale(1.05);
}

.resource-card h3 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: #333;
}

.resource-card p {
    color: #666;
    margin-bottom: 1.5rem;
}

.resource-link {
    color: #2d5a27;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.resource-link:hover {
    color: #4a7c59;
}

/* Contact Section */
.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    max-width: 1000px;
    margin: 0 auto;
}

.contact-info h3 {
    font-size: 1.5rem;
    color: #2d5a27;
    margin-bottom: 2rem;
}

.contact-item {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
}

.contact-item i {
    font-size: 1.2rem;
    color: #2d5a27;
    margin-right: 1rem;
    width: 20px;
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
}

.social-link {
    width: 50px;
    height: 50px;
    background: #2d5a27;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-link:hover {
    background: #4a7c59;
    transform: translateY(-3px);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 15px;
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
    font-family: inherit;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #2d5a27;
}

/* Footer */
.footer {
    background: #1a1a1a;
    color: white;
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 1rem;
    color: #2d5a27;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: #ccc;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: white;
}

.footer-bottom {
    border-top: 1px solid #333;
    padding-top: 1rem;
    text-align: center;
    color: #999;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }
    
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: white;
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0,0,0,0.05);
        padding: 2rem 0;
    }
    
    .nav-menu.active {
        left: 0;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }

    .hero-stats {
        flex-direction: column;
        gap: 2rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .about-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .about-img {
        height: 300px;
    }

    .logo-img {
        height: 40px;
    }

    .nav-logo h2 {
        font-size: 1.5rem;
    }
    
    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .values {
        grid-template-columns: 1fr;
    }
    
    .programs-grid {
        grid-template-columns: 1fr;
    }
    
    .impact-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .resources-grid {
        grid-template-columns: 1fr;
    }

    .conservation-grid {
        grid-template-columns: 1fr;
    }

    .campaign-stats {
        flex-direction: column;
        gap: 2rem;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }

    .impact-grid {
        grid-template-columns: 1fr;
    }

    .section-header h2 {
        font-size: 2rem;
    }

    .program-img,
    .resource-img {
        height: 150px;
    }

    .about-img {
        height: 250px;
    }

    .logo-img {
        height: 35px;
    }

    .nav-logo h2 {
        font-size: 1.3rem;
    }
}
